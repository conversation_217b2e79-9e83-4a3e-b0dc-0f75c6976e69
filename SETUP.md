# VPSScriptHelper-BlueBlue Setup Guide

This guide will walk you through setting up the VPSScriptHelper-BlueBlue application step by step.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [First Run](#first-run)
- [Adding Servers](#adding-servers)
- [Testing](#testing)
- [Production Deployment](#production-deployment)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **Operating System**: Windows, Linux, or macOS
- **Python**: Version 3.8 or higher
- **Memory**: Minimum 512MB RAM
- **Storage**: At least 1GB free space
- **Network**: Internet connection for package installation

### Required Software
1. **Python 3.8+**
   ```bash
   # Check Python version
   python --version
   # or
   python3 --version
   ```

2. **pip (Python Package Manager)**
   ```bash
   # Check pip version
   pip --version
   ```

3. **Git** (optional, for cloning repository)
   ```bash
   # Check Git version
   git --version
   ```

## Installation

### Step 1: Download the Project
```bash
# Option 1: Clone with Git
git clone <repository-url>
cd VPSScriptHelper-BlueBlue

# Option 2: Download and extract ZIP file
# Extract to desired directory and navigate to it
```

### Step 2: Create Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate

# On Linux/macOS:
source venv/bin/activate
```

### Step 3: Install Dependencies
```bash
# Install required packages
pip install -r requirements.txt

# If you encounter issues, install core packages individually:
pip install fastapi uvicorn sqlalchemy aiosqlite
pip install pydantic-settings python-dotenv email-validator
pip install python-jose[cryptography] passlib[bcrypt]
pip install paramiko apscheduler httpx
```

### Step 4: Run Setup Script
```bash
# Run the automated setup
python start.py
```

This will:
- Create the `.env` configuration file
- Set up necessary directories
- Initialize the database
- Create default admin user

## Configuration

### Environment Variables (.env file)
After running the setup, edit the `.env` file to customize your settings:

```bash
# Application Settings
DEBUG=true                    # Set to false for production
HOST=0.0.0.0                 # Server host
PORT=8000                    # Server port

# Security Settings
SECRET_KEY=your-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Settings
DATABASE_URL=sqlite+aiosqlite:///./blueblue.db

# SSH Settings
SSH_TIMEOUT=30               # SSH connection timeout
SSH_RETRY_ATTEMPTS=3         # Number of retry attempts
SSH_RETRY_DELAY=5           # Delay between retries

# Xray Configuration
XRAY_CONFIG_PATH=/etc/xray/config.json
XRAY_SERVICE_NAME=xray

# Task Scheduling
EXPIRY_CHECK_INTERVAL_HOURS=24    # How often to check for expired clients
AUTO_REMOVE_EXPIRED=true         # Automatically remove expired clients

# Logging
LOG_LEVEL=INFO              # DEBUG, INFO, WARNING, ERROR
```

### Important Security Notes
1. **Change the SECRET_KEY** - Generate a secure random key for production
2. **Update default password** - Change admin password after first login
3. **Set proper CORS origins** - Restrict ALLOWED_HOSTS for production

## First Run

### Step 1: Start the Application
```bash
# Start the development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# For production (without reload):
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Step 2: Verify Installation
1. **Check Application Status**
   ```bash
   curl http://localhost:8000/
   ```

2. **Check Health**
   ```bash
   curl http://localhost:8000/api/v1/health/
   ```

3. **Access API Documentation**
   - Open browser: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc

### Step 3: Login with Default Credentials
- **Username**: `admin`
- **Password**: `admin123`
- **⚠️ Important**: Change this password immediately!

## Adding Servers

### Method 1: Using API Documentation (Recommended for beginners)
1. Go to http://localhost:8000/docs
2. Click on "Authorize" button
3. Login with admin credentials
4. Use the server endpoints to add your servers

### Method 2: Using curl commands
```bash
# 1. Get authentication token
TOKEN=$(curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}' \
     | jq -r '.access_token')

# 2. Add a server (example)
curl -X POST "http://localhost:8000/api/v1/servers/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "My VPS Server",
       "host": "*************",
       "port": 22,
       "username": "root",
       "password": "your_password",
       "description": "Main VPN server",
       "is_active": true
     }'
```

### Server Configuration Options
- **Password Authentication**: Provide `password` field
- **SSH Key Authentication**: Provide `private_key` and optionally `private_key_passphrase`
- **Custom Xray Paths**: Set `xray_config_path` and `xray_service_name`

## Testing

### Test Server Connection
```bash
# Test SSH connection to a server
curl -X POST "http://localhost:8000/api/v1/servers/1/test-connection" \
     -H "Authorization: Bearer $TOKEN"
```

### Test Xray Service
```bash
# Check Xray service status
curl -X GET "http://localhost:8000/api/v1/servers/1/service-status" \
     -H "Authorization: Bearer $TOKEN"

# Restart Xray service
curl -X POST "http://localhost:8000/api/v1/servers/1/restart-xray" \
     -H "Authorization: Bearer $TOKEN"
```

### Test Configuration Management
```bash
# Get server configuration
curl -X GET "http://localhost:8000/api/v1/servers/1/config" \
     -H "Authorization: Bearer $TOKEN"

# Get clients from configuration
curl -X GET "http://localhost:8000/api/v1/servers/1/clients" \
     -H "Authorization: Bearer $TOKEN"
```

### Test Expiry Management
```bash
# Get expiry summary
curl -X GET "http://localhost:8000/api/v1/tasks/expiry/summary" \
     -H "Authorization: Bearer $TOKEN"

# Trigger manual expiry check
curl -X POST "http://localhost:8000/api/v1/tasks/expiry-check" \
     -H "Authorization: Bearer $TOKEN"

# Remove expired clients from specific server
curl -X POST "http://localhost:8000/api/v1/servers/1/remove-expired" \
     -H "Authorization: Bearer $TOKEN"
```

## Production Deployment

### Step 1: Environment Configuration
```bash
# Set production environment
ENVIRONMENT=production

# Use strong secret key
SECRET_KEY=your-very-secure-random-secret-key

# Disable debug mode
DEBUG=false

# Set specific allowed hosts
ALLOWED_HOSTS=yourdomain.com,localhost

# Use stronger logging
LOG_LEVEL=WARNING
```

### Step 2: Database Setup
For production, consider using PostgreSQL instead of SQLite:
```bash
# Install PostgreSQL driver
pip install asyncpg

# Update DATABASE_URL in .env
DATABASE_URL=postgresql+asyncpg://user:password@localhost/blueblue_db
```

### Step 3: Process Management
Use a process manager like systemd, supervisor, or PM2:

**Example systemd service file** (`/etc/systemd/system/blueblue.service`):
```ini
[Unit]
Description=VPSScriptHelper-BlueBlue
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/VPSScriptHelper-BlueBlue
Environment=PATH=/path/to/VPSScriptHelper-BlueBlue/venv/bin
ExecStart=/path/to/VPSScriptHelper-BlueBlue/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

### Step 4: Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Error: ModuleNotFoundError
# Solution: Ensure virtual environment is activated and dependencies installed
pip install -r requirements.txt
```

#### 2. Database Connection Issues
```bash
# Error: Database connection failed
# Solution: Check database file permissions and path
ls -la blueblue.db
chmod 664 blueblue.db
```

#### 3. SSH Connection Failures
```bash
# Error: SSH connection failed
# Solutions:
# - Verify server credentials
# - Check network connectivity
# - Ensure SSH service is running on target server
# - Verify firewall settings
```

#### 4. Permission Errors
```bash
# Error: Permission denied
# Solution: Ensure proper file permissions
chmod +x start.py
chmod -R 755 logs/
chmod -R 755 uploads/
chmod -R 755 backups/
```

#### 5. Port Already in Use
```bash
# Error: Port 8000 already in use
# Solution: Change port in .env file or kill existing process
lsof -ti:8000 | xargs kill -9
# or change PORT=8001 in .env
```

### Log Files
Check these log files for detailed error information:
- `logs/app.log` - Application logs
- `logs/error.log` - Error logs

### Debug Mode
Enable debug mode for detailed error messages:
```bash
# In .env file
DEBUG=true
LOG_LEVEL=DEBUG
```

### Getting Help
1. Check the logs first
2. Verify configuration settings
3. Test individual components
4. Check network connectivity
5. Verify server credentials

## Next Steps

After successful setup:
1. **Change default password**
2. **Add your BlueBlue servers**
3. **Test all functionality**
4. **Set up monitoring**
5. **Configure backups**
6. **Review security settings**

For ongoing maintenance, monitor the logs and health endpoints regularly.

## Advanced Configuration

### Custom Xray Configuration Template
You can customize the default Xray configuration template in `config/xray_template.json`:

```json
{
    "log": {
        "access": "/var/log/xray/access.log",
        "error": "/var/log/xray/error.log",
        "loglevel": "warning"
    },
    "inbounds": [
        {
            "listen": "127.0.0.1",
            "port": "23174",
            "protocol": "vless",
            "settings": {
                "decryption": "none",
                "clients": []
            }
        }
    ]
}
```

### Automated Backup Script
Create a backup script for your configurations:

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups/automated"
mkdir -p $BACKUP_DIR

# Backup database
cp blueblue.db "$BACKUP_DIR/blueblue_$DATE.db"

# Backup configuration
cp .env "$BACKUP_DIR/env_$DATE.backup"

# Backup logs (last 1000 lines)
tail -1000 logs/app.log > "$BACKUP_DIR/app_log_$DATE.log"

echo "Backup completed: $BACKUP_DIR"
```

### Monitoring Setup
Set up monitoring with health check endpoints:

```bash
#!/bin/bash
# monitor.sh
HEALTH_URL="http://localhost:8000/api/v1/health/detailed"
RESPONSE=$(curl -s $HEALTH_URL)
STATUS=$(echo $RESPONSE | jq -r '.status')

if [ "$STATUS" != "healthy" ]; then
    echo "Application unhealthy: $RESPONSE"
    # Send alert (email, Slack, etc.)
fi
```

### SSL/TLS Configuration
For production with SSL:

```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
    }
}
```

## API Usage Examples

### Complete Workflow Example
```bash
#!/bin/bash
# complete_workflow.sh

BASE_URL="http://localhost:8000"

# 1. Login and get token
echo "Logging in..."
TOKEN=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}' \
     | jq -r '.access_token')

echo "Token: $TOKEN"

# 2. Add a server
echo "Adding server..."
SERVER_ID=$(curl -s -X POST "$BASE_URL/api/v1/servers/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Production Server",
       "host": "*************",
       "port": 22,
       "username": "root",
       "password": "your_password",
       "description": "Main production server"
     }' | jq -r '.id')

echo "Server ID: $SERVER_ID"

# 3. Test connection
echo "Testing connection..."
curl -s -X POST "$BASE_URL/api/v1/servers/$SERVER_ID/test-connection" \
     -H "Authorization: Bearer $TOKEN" | jq '.'

# 4. Get server configuration
echo "Getting configuration..."
curl -s -X GET "$BASE_URL/api/v1/servers/$SERVER_ID/config" \
     -H "Authorization: Bearer $TOKEN" | jq '.'

# 5. Check for expired clients
echo "Checking expiry..."
curl -s -X POST "$BASE_URL/api/v1/tasks/expiry-check" \
     -H "Authorization: Bearer $TOKEN" | jq '.'
```

### Python Client Example
```python
#!/usr/bin/env python3
# client_example.py

import httpx
import asyncio
import json

class BlueBlueClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.token = None

    async def login(self, username, password):
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/auth/login",
                json={"username": username, "password": password}
            )
            if response.status_code == 200:
                self.token = response.json()["access_token"]
                return True
            return False

    async def get_servers(self):
        headers = {"Authorization": f"Bearer {self.token}"}
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/servers/",
                headers=headers
            )
            return response.json()

    async def test_server_connection(self, server_id):
        headers = {"Authorization": f"Bearer {self.token}"}
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/servers/{server_id}/test-connection",
                headers=headers
            )
            return response.json()

# Usage example
async def main():
    client = BlueBlueClient()

    # Login
    if await client.login("admin", "admin123"):
        print("Login successful")

        # Get servers
        servers = await client.get_servers()
        print(f"Found {len(servers)} servers")

        # Test first server connection
        if servers:
            result = await client.test_server_connection(servers[0]["id"])
            print(f"Connection test: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Maintenance

### Regular Tasks
1. **Daily**: Check application health and logs
2. **Weekly**: Review expired clients and cleanup
3. **Monthly**: Update dependencies and security patches
4. **Quarterly**: Full backup and disaster recovery test

### Update Procedure
```bash
# 1. Backup current installation
cp -r VPSScriptHelper-BlueBlue VPSScriptHelper-BlueBlue.backup

# 2. Pull latest changes
git pull origin main

# 3. Update dependencies
pip install -r requirements.txt --upgrade

# 4. Run database migrations (if any)
# python migrate.py

# 5. Restart application
systemctl restart blueblue
```

### Performance Optimization
- Monitor database size and optimize queries
- Set up log rotation to prevent disk space issues
- Use connection pooling for high-traffic scenarios
- Consider Redis for caching if needed

This comprehensive setup guide should help you get the VPSScriptHelper-BlueBlue application running smoothly in any environment!
