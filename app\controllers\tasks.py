"""
Task management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List
import logging

from app.core.database import get_db
from app.dependencies import get_current_user
from app.models.user import User
from app.models.task import Task
from app.services.expiry_service import ExpiryService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/")
async def get_tasks(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of tasks."""
    result = await db.execute(select(Task).offset(skip).limit(limit).order_by(Task.created_at.desc()))
    tasks = result.scalars().all()
    return [task.to_dict() for task in tasks]


@router.get("/{task_id}")
async def get_task(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get task by ID."""
    result = await db.execute(select(Task).where(Task.id == task_id))
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return task.to_dict()


@router.post("/expiry-check")
async def trigger_expiry_check(
    server_id: int = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Trigger manual expiry check."""
    expiry_service = ExpiryService()
    
    if server_id:
        # Check specific server
        result = await expiry_service.force_remove_expired_clients(server_id)
        return result
    else:
        # Check all servers
        result = await expiry_service.check_all_servers_expiry()
        return result


@router.get("/expiry/summary")
async def get_expiry_summary(
    current_user: User = Depends(get_current_user)
):
    """Get expiry summary for all servers."""
    expiry_service = ExpiryService()
    summary = await expiry_service.get_expiry_summary()
    return summary
