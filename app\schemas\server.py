"""
Server-related Pydantic schemas
"""

from pydantic import BaseModel, field_validator
from typing import Optional, List
from datetime import datetime


class ServerBase(BaseModel):
    """Base server schema."""
    name: str
    host: str
    port: int = 22
    username: str
    description: Optional[str] = None
    xray_config_path: str = "/etc/xray/config.json"
    xray_service_name: str = "xray"
    is_active: bool = True
    tags: List[str] = []
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if len(v.strip()) < 1:
            raise ValueError('Server name cannot be empty')
        return v.strip()
    
    @field_validator('host')
    @classmethod
    def validate_host(cls, v):
        if len(v.strip()) < 1:
            raise ValueError('Host cannot be empty')
        return v.strip()
    
    @field_validator('port')
    @classmethod
    def validate_port(cls, v):
        if not (1 <= v <= 65535):
            raise ValueError('Port must be between 1 and 65535')
        return v


class ServerCreate(ServerBase):
    """Schema for creating a new server."""
    password: Optional[str] = None
    private_key: Optional[str] = None
    private_key_passphrase: Optional[str] = None
    
    @field_validator('password', 'private_key')
    @classmethod
    def validate_auth_method(cls, v, info):
        # At least one authentication method must be provided
        return v


class ServerUpdate(BaseModel):
    """Schema for updating server information."""
    name: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    private_key: Optional[str] = None
    private_key_passphrase: Optional[str] = None
    description: Optional[str] = None
    xray_config_path: Optional[str] = None
    xray_service_name: Optional[str] = None
    is_active: Optional[bool] = None
    tags: Optional[List[str]] = None


class ServerResponse(ServerBase):
    """Schema for server response."""
    id: int
    last_connected: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    health_status: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ServerConnectionTest(BaseModel):
    """Schema for connection test results."""
    success: bool
    connection_time: Optional[float] = None
    message: str
    error: Optional[str] = None 