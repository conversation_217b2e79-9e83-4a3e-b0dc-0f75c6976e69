# VPSScriptHelper-BlueBlue

A FastAPI application for centralized control and management of multiple BlueBlue VPN servers.

## About

This project is built on [BlueBlue](https://github.com/NevermoreSSH/Blueblue), providing a centralized web interface to manage multiple Xray VPN servers across different VPS instances.

## Features

- **Multi-Server Management**: Control multiple BlueBlue servers from a single interface
- **Client Management**: Add, remove, and manage VPN clients across servers
- **Automatic Expiry Detection**: Automatically detect and remove expired clients
- **SSH Integration**: Secure SSH/SFTP connections for remote server management
- **Service Control**: Start, stop, and restart Xray services remotely
- **Health Monitoring**: Monitor server status and connectivity
- **RESTful API**: Complete REST API for programmatic access

## Technology Stack

- **Backend**: FastAPI (Python)
- **Database**: SQLite with SQLAlchemy ORM
- **SSH/SFTP**: Paramiko for secure connections
- **Task Scheduling**: APScheduler for automated tasks
- **Authentication**: JWT-based authentication
- **Documentation**: Auto-generated OpenAPI/Swagger docs

## Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package manager)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd VPSScriptHelper-BlueBlue
```

2. Create a virtual environment (recommended):
```bash
python -m venv venv
# On Windows:
venv\Scripts\activate
# On Linux/Mac:
source venv/bin/activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Setup the application:
```bash
python start.py
```

5. Start the application:
```bash
uvicorn main:app --reload
```

6. Access the application:
- API Documentation: `http://localhost:8000/docs`
- Alternative docs: `http://localhost:8000/redoc`
- Health check: `http://localhost:8000/api/v1/health`

### Default Credentials
- **Username**: `admin`
- **Password**: `admin123`
- **⚠️ Important**: Change the password after first login!

### First Steps After Setup

1. **Login and change password**:
   - Use the `/api/v1/auth/login` endpoint to get an access token
   - Update your password using the user management endpoints

2. **Add your servers**:
   - Use the server management endpoints to add your BlueBlue servers
   - Configure SSH connection details (password or private key)
   - Test connections using the test endpoint

3. **Configure automatic expiry checking**:
   - The system automatically checks for expired clients every 24 hours
   - You can trigger manual checks using the task endpoints
   - Configure auto-removal settings in the `.env` file

## Configuration

The application manages Xray configuration files (`/etc/xray/config.json`) on remote servers, handling client configurations like:

```json
{
    "id": "05807d1c-1646-4e0e-939f-195a560793ac",
    "shopeeUsername": "240715NXGCYW98-MG",
    "expired_date": "01-03-2025",
    "email": "240715NXGCYW98-MG"
}
```

## API Endpoints

### Authentication (`/api/v1/auth/`)
- `POST /register` - Register new user
- `POST /login` - User login (returns JWT token)
- `GET /me` - Get current user info
- `POST /logout` - User logout
- `POST /refresh` - Refresh access token

### Server Management (`/api/v1/servers/`)
- `GET /` - List all servers
- `GET /{server_id}` - Get server details
- `POST /{server_id}/test-connection` - Test SSH connection
- `POST /{server_id}/restart-xray` - Restart Xray service
- `GET /{server_id}/service-status` - Get Xray service status
- `GET /{server_id}/config` - Get Xray configuration
- `GET /{server_id}/clients` - Get clients from config
- `POST /{server_id}/remove-expired` - Remove expired clients

### Client Management (`/api/v1/clients/`)
- `GET /` - List clients (TODO)
- `POST /` - Create new client (TODO)
- `GET /{client_id}` - Get client details (TODO)
- `PUT /{client_id}` - Update client (TODO)
- `DELETE /{client_id}` - Delete client (TODO)

### Configuration (`/api/v1/config/`)
- `GET /` - Configuration overview (TODO)
- `POST /backup` - Backup configurations (TODO)
- `POST /restore` - Restore configuration (TODO)

### Task Management (`/api/v1/tasks/`)
- `GET /` - List tasks
- `GET /{task_id}` - Get task details
- `POST /expiry-check` - Trigger expiry check
- `GET /expiry/summary` - Get expiry summary

### Health Monitoring (`/api/v1/health/`)
- `GET /` - Basic health check
- `GET /detailed` - Detailed health check
- `GET /expiry-summary` - Expiry status overview
- `GET /servers/{server_id}` - Server-specific health check

## Usage Examples

### Login and Get Token
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}'
```

### List Servers
```bash
curl -X GET "http://localhost:8000/api/v1/servers/" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Test Server Connection
```bash
curl -X POST "http://localhost:8000/api/v1/servers/1/test-connection" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Remove Expired Clients
```bash
curl -X POST "http://localhost:8000/api/v1/servers/1/remove-expired" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Trigger Manual Expiry Check
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/expiry-check" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Development

### Development Authentication Bypass

For easier debugging and testing, you can disable authentication in development mode:

1. **Enable bypass in `.env`:**
   ```env
   ENVIRONMENT=development
   DEBUG=true
   DISABLE_AUTH_IN_DEV=true
   ```

2. **Test without authentication:**
   ```bash
   # No Bearer token needed in development mode
   curl http://localhost:8000/api/v1/servers/
   curl http://localhost:8000/api/v1/auth/me
   ```

3. **See detailed documentation:** `DEVELOPMENT_AUTH.md`

⚠️ **Important**: This bypass only works in development mode and is automatically disabled in production.

### Project Structure
See `folder_structure.md` for detailed project organization.

### Adding New Features
1. Create models in `app/models/`
2. Add schemas in `app/schemas/`
3. Implement services in `app/services/`
4. Create controllers in `app/controllers/`
5. Update main.py to include new routes

### Testing
- Use the built-in API documentation at `/docs` for interactive testing
- Health checks available at `/api/v1/health/`
- Logs are stored in the `logs/` directory

## Troubleshooting

### Common Issues
1. **Database connection errors**: Check if the database file exists and is writable
2. **SSH connection failures**: Verify server credentials and network connectivity
3. **Permission errors**: Ensure the application has proper file system permissions
4. **Port conflicts**: Change the port in `.env` if 8000 is already in use

### Logs
- Application logs: `logs/app.log`
- Error logs: `logs/error.log`
- Set `LOG_LEVEL=DEBUG` in `.env` for detailed logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is based on [BlueBlue](https://github.com/NevermoreSSH/Blueblue) and follows the same licensing terms.