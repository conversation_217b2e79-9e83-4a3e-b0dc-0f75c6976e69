"""
Server model for database
"""

from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class Server(Base):
    """Server model representing a BlueBlue VPN server."""
    
    __tablename__ = "servers"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True, nullable=False)
    host = Column(String(255), nullable=False)
    port = Column(Integer, default=22, nullable=False)
    username = Column(String(100), nullable=False)
    password = Column(String(255), nullable=True)  # Encrypted
    private_key = Column(Text, nullable=True)  # SSH private key
    private_key_passphrase = Column(String(255), nullable=True)  # Encrypted
    
    # Server configuration
    xray_config_path = Column(String(500), default="/etc/xray/config.json")
    xray_service_name = Column(String(100), default="xray")
    
    # Status and metadata
    is_active = Column(Boolean, default=True)
    last_connected = Column(DateTime, nullable=True)
    last_health_check = Column(DateTime, nullable=True)
    health_status = Column(String(50), default="unknown")  # healthy, unhealthy, unknown
    
    # Additional metadata
    description = Column(Text, nullable=True)
    tags = Column(JSON, default=list)  # List of tags for categorization
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    clients = relationship("Client", back_populates="server", cascade="all, delete-orphan")
    tasks = relationship("Task", back_populates="server", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Server(id={self.id}, name='{self.name}', host='{self.host}')>"
    
    @property
    def connection_info(self):
        """Get connection information dictionary."""
        return {
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "password": self.password,
            "private_key": self.private_key,
            "private_key_passphrase": self.private_key_passphrase
        }
    
    def to_dict(self, include_client_count=False):
        """Convert server to dictionary (excluding sensitive data)."""
        result = {
            "id": self.id,
            "name": self.name,
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "xray_config_path": self.xray_config_path,
            "xray_service_name": self.xray_service_name,
            "is_active": self.is_active,
            "last_connected": self.last_connected.isoformat() if self.last_connected else None,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "health_status": self.health_status,
            "description": self.description,
            "tags": self.tags,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

        # Only include client count if explicitly requested to avoid lazy loading issues
        if include_client_count:
            try:
                result["client_count"] = len(self.clients) if self.clients else 0
            except:
                result["client_count"] = 0

        return result
