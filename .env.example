# Environment Configuration
ENVIRONMENT=development

# Application Settings
APP_NAME=VPSScriptHelper-BlueBlue
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Security Settings
SECRET_KEY=your-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Settings
DATABASE_URL=sqlite+aiosqlite:///./blueblue.db

# CORS Settings (comma-separated list)
ALLOWED_HOSTS=*

# SSH Settings
SSH_TIMEOUT=30
SSH_RETRY_ATTEMPTS=3
SSH_RETRY_DELAY=5

# Xray Configuration
XRAY_CONFIG_PATH=/etc/xray/config.json
XRAY_SERVICE_NAME=xray
XRAY_BACKUP_PATH=/etc/xray/config.json.backup

# Task Scheduling
EXPIRY_CHECK_INTERVAL_HOURS=24
AUTO_REMOVE_EXPIRED=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=

# File Paths
UPLOAD_DIR=./uploads
BACKUP_DIR=./backups
