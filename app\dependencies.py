"""
FastAPI dependencies
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional

from app.core.database import get_db
from app.core.security import verify_token
from app.core.config import settings
from app.models.user import User

security = HTTPBearer(auto_error=False)


def create_dev_user() -> User:
    """Create a mock user for development mode."""
    user = User()
    user.id = 1
    user.username = "dev_user"
    user.email = "<EMAIL>"
    user.full_name = "Development User"
    user.is_active = True
    user.is_superuser = True
    user.login_count = 0
    return user


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Get current authenticated user.
    In development mode with DISABLE_AUTH_IN_DEV=True, returns a mock user.

    Args:
        credentials: HTTP Bearer token
        db: Database session

    Returns:
        User: Current authenticated user

    Raises:
        HTTPException: If authentication fails
    """
    # Bypass authentication in development mode
    if settings.DISABLE_AUTH_IN_DEV and settings.DEBUG:
        return create_dev_user()

    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )

    try:
        payload = verify_token(credentials.credentials)
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )

    result = await db.execute(select(User).where(User.username == username))
    user = result.scalar_one_or_none()

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user"
        )

    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Get current active user.
    In development mode, always returns the mock user as active.

    Args:
        current_user: Current user from authentication

    Returns:
        User: Current active user

    Raises:
        HTTPException: If user is inactive
    """
    # In development mode, mock user is always active
    if settings.DISABLE_AUTH_IN_DEV and settings.DEBUG:
        return current_user

    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_superuser(current_user: User = Depends(get_current_user)) -> User:
    """
    Get current superuser.
    In development mode, always returns the mock user as superuser.

    Args:
        current_user: Current user from authentication

    Returns:
        User: Current superuser

    Raises:
        HTTPException: If user is not a superuser
    """
    # In development mode, mock user is always superuser
    if settings.DISABLE_AUTH_IN_DEV and settings.DEBUG:
        return current_user

    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    Get current user if authenticated, otherwise return None.
    In development mode, always returns the mock user.

    Args:
        credentials: Optional HTTP Bearer token
        db: Database session

    Returns:
        Optional[User]: Current user or None
    """
    # In development mode, always return mock user
    if settings.DISABLE_AUTH_IN_DEV and settings.DEBUG:
        return create_dev_user()

    if not credentials:
        return None

    try:
        payload = verify_token(credentials.credentials)
        username: str = payload.get("sub")
        if username is None:
            return None
    except Exception:
        return None

    try:
        result = db.execute(select(User).where(User.username == username))
        user = result.scalar_one_or_none()

        if user and user.is_active:
            return user
    except Exception:
        pass

    return None
