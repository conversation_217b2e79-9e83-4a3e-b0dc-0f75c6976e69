# Development Authentication Bypass

This document explains how to use the development authentication bypass feature to debug API endpoints without needing to authenticate.

## Overview

In development mode, you can disable authentication requirements for all API endpoints. This allows you to test and debug endpoints directly without having to:
- Create user accounts
- Login to get JWT tokens
- Include Bearer tokens in requests

## Configuration

### Environment Variables

The authentication bypass is controlled by these environment variables in `.env`:

```env
# Environment Configuration
ENVIRONMENT=development

# Application Settings
DEBUG=true

# Development Settings
DISABLE_AUTH_IN_DEV=true
```

### How It Works

When both `DEBUG=true` and `DISABLE_AUTH_IN_DEV=true` are set:

1. **All protected endpoints bypass authentication**
2. **A mock development user is automatically used** with these properties:
   - ID: 1
   - Username: "dev_user"
   - Email: "<EMAIL>"
   - Is Active: true
   - Is Superuser: true

3. **No Bearer tokens are required** in API requests

## Testing the Setup

### Quick Test

Run the test script to verify the configuration:

```bash
python test_simple_auth.py
```

### Manual Testing

1. **Start the server:**
   ```bash
   uvicorn main:app --reload
   ```

2. **Open the API documentation:**
   ```
   http://localhost:8000/docs
   ```

3. **Test any endpoint without authentication:**
   - Click on any endpoint (e.g., `/api/v1/servers/`)
   - Click "Try it out"
   - Click "Execute" (without adding any Authorization header)
   - The request should succeed with a 200 status code

### Example API Calls

With authentication bypass enabled, these calls work without tokens:

```bash
# Get all servers
curl http://localhost:8000/api/v1/servers/

# Get current user info (returns mock dev user)
curl http://localhost:8000/api/v1/auth/me

# Get health status
curl http://localhost:8000/api/v1/health/detailed

# Get tasks
curl http://localhost:8000/api/v1/tasks/
```

## Security Notes

⚠️ **IMPORTANT**: This feature should ONLY be used in development!

- **Never enable this in production**
- The bypass only works when `DEBUG=true`
- Production settings automatically disable this feature
- Always test with authentication enabled before deploying

## Disabling the Bypass

To re-enable authentication (for testing production-like behavior):

1. **Set in `.env`:**
   ```env
   DISABLE_AUTH_IN_DEV=false
   ```

2. **Or change environment:**
   ```env
   ENVIRONMENT=production
   ```

3. **Restart the application**

## Troubleshooting

### Bypass Not Working

If endpoints still require authentication:

1. **Check environment variables:**
   ```bash
   python test_simple_auth.py
   ```

2. **Verify `.env` file:**
   ```env
   ENVIRONMENT=development
   DEBUG=true
   DISABLE_AUTH_IN_DEV=true
   ```

3. **Restart the application** after changing `.env`

### Still Getting 401 Errors

- Ensure you're not sending Authorization headers
- Check that the server restarted after configuration changes
- Verify the endpoint is using the correct dependencies from `app.dependencies`

## Implementation Details

The bypass is implemented in `app/dependencies.py`:

- `get_current_user()` returns a mock user when bypass is enabled
- `get_current_active_user()` and `get_current_superuser()` also use the mock user
- All controllers import dependencies from `app.dependencies` (not `app.controllers.auth`)

This ensures consistent behavior across all endpoints.
