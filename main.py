"""
VPSScriptHelper-BlueBlue FastAPI Application
Main entry point for the centralized BlueBlue server management system.
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from contextlib import asynccontextmanager
import logging
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from app.core.config import settings
from app.core.database import engine, Base
from app.core.exceptions import CustomException
from app.controllers import auth, servers, clients, config, tasks, health
from app.services.expiry_service import ExpiryService
from app.utils.logger import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global scheduler instance
scheduler = AsyncIOScheduler()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting VPSScriptHelper-BlueBlue application...")
    
    # Create database tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Start the scheduler for automated tasks
    scheduler.start()
    logger.info("Scheduler started")
    
    # Add periodic expiry check task
    expiry_service = ExpiryService()
    scheduler.add_job(
        expiry_service.check_all_servers_expiry,
        'interval',
        hours=settings.EXPIRY_CHECK_INTERVAL_HOURS,
        id='expiry_check',
        replace_existing=True
    )
    logger.info(f"Expiry check scheduled every {settings.EXPIRY_CHECK_INTERVAL_HOURS} hours")
    
    yield
    
    # Shutdown
    logger.info("Shutting down application...")
    scheduler.shutdown()
    logger.info("Scheduler stopped")


# Create FastAPI application
app = FastAPI(
    title="VPSScriptHelper-BlueBlue",
    description="Centralized management system for BlueBlue VPN servers",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_hosts_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(CustomException)
async def custom_exception_handler(request, exc: CustomException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "error_code": exc.error_code}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "error_code": "INTERNAL_ERROR"}
    )


# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(servers.router, prefix="/api/v1/servers", tags=["Servers"])
app.include_router(clients.router, prefix="/api/v1/clients", tags=["Clients"])
app.include_router(config.router, prefix="/api/v1/config", tags=["Configuration"])
app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["Tasks"])
app.include_router(health.router, prefix="/api/v1/health", tags=["Health"])


@app.get("/")
async def root():
    """Root endpoint with basic application information."""
    return {
        "message": "VPSScriptHelper-BlueBlue API",
        "version": "1.0.0",
        "description": "Centralized management system for BlueBlue VPN servers",
        "docs_url": "/docs",
        "health_check": "/api/v1/health"
    }


@app.get("/api/v1")
async def api_info():
    """API version information."""
    return {
        "api_version": "v1",
        "endpoints": {
            "authentication": "/api/v1/auth",
            "servers": "/api/v1/servers",
            "clients": "/api/v1/clients",
            "configuration": "/api/v1/config",
            "tasks": "/api/v1/tasks",
            "health": "/api/v1/health"
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
