"""
Server management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List
import logging

from app.core.database import get_db
from app.dependencies import get_current_user
from app.models.server import Server
from app.models.user import User
from app.services.ssh_service import SSHService
from app.services.config_service import ConfigService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_servers(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of servers."""
    result = await db.execute(select(Server).offset(skip).limit(limit))
    servers = result.scalars().all()
    return [server.to_dict() for server in servers]


@router.get("/{server_id}", response_model=dict)
async def get_server(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get server by ID."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    return server.to_dict()


@router.post("/{server_id}/test-connection")
async def test_server_connection(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Test SSH connection to server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    ssh_service = SSHService()
    connection_result = ssh_service.test_connection(server)
    
    return connection_result


@router.post("/{server_id}/restart-xray")
async def restart_xray_service(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Restart Xray service on server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    ssh_service = SSHService()
    restart_result = ssh_service.restart_xray_service(server)
    
    return restart_result


@router.get("/{server_id}/service-status")
async def get_service_status(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get Xray service status."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    ssh_service = SSHService()
    status_result = ssh_service.get_service_status(server)
    
    return status_result


@router.get("/{server_id}/config")
async def get_server_config(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get Xray configuration from server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    try:
        config_service = ConfigService()
        config = config_service.get_config(server)
        return config
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get configuration: {str(e)}"
        )


@router.get("/{server_id}/clients")
async def get_server_clients(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get clients from server configuration."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    try:
        config_service = ConfigService()
        clients = config_service.get_clients_from_config(server)
        return {"clients": clients}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get clients: {str(e)}"
        )


@router.post("/{server_id}/remove-expired")
async def remove_expired_clients(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Remove expired clients from server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    try:
        config_service = ConfigService()
        removal_result = config_service.remove_expired_clients(server)
        
        # Restart Xray service if clients were removed
        if removal_result.get("success") and removal_result.get("removed_count", 0) > 0:
            ssh_service = SSHService()
            restart_result = ssh_service.restart_xray_service(server)
            removal_result["restart_result"] = restart_result
        
        return removal_result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove expired clients: {str(e)}"
        )
