"""
Server management API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List
import logging

from app.core.database import get_db
from app.dependencies import get_current_user
from app.models.server import Server
from app.models.user import User
from app.schemas.server import ServerCreate, ServerUpdate, ServerResponse
from app.services.ssh_service import SSHService
from app.services.config_service import ConfigService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=List[ServerResponse])
async def get_servers(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of servers."""
    result = await db.execute(select(Server).offset(skip).limit(limit))
    servers = result.scalars().all()
    return [ServerResponse.model_validate(server) for server in servers]


@router.post("/", response_model=ServerResponse)
async def create_server(
    server_data: ServerCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new server."""
    # Check if server name already exists
    result = await db.execute(select(Server).where(Server.name == server_data.name))
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Server name already exists"
        )
    
    # Validate that at least one authentication method is provided
    if not server_data.password and not server_data.private_key:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either password or private_key must be provided"
        )
    
    # Create new server
    server = Server(
        name=server_data.name,
        host=server_data.host,
        port=server_data.port,
        username=server_data.username,
        password=server_data.password,
        private_key=server_data.private_key,
        private_key_passphrase=server_data.private_key_passphrase,
        description=server_data.description,
        xray_config_path=server_data.xray_config_path,
        xray_service_name=server_data.xray_service_name,
        is_active=server_data.is_active,
        tags=server_data.tags
    )
    
    db.add(server)
    await db.commit()
    await db.refresh(server)
    
    logger.info(f"New server created: {server.name} by user {current_user.username}")
    return ServerResponse.model_validate(server)


@router.get("/{server_id}", response_model=ServerResponse)
async def get_server(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get server by ID."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    return ServerResponse.model_validate(server)


@router.put("/{server_id}", response_model=ServerResponse)
async def update_server(
    server_id: int,
    server_data: ServerUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update server information."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    # Check if new name conflicts with existing server
    if server_data.name and server_data.name != server.name:
        result = await db.execute(select(Server).where(Server.name == server_data.name))
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Server name already exists"
            )
    
    # Update server fields
    update_data = server_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(server, field, value)
    
    await db.commit()
    await db.refresh(server)
    
    logger.info(f"Server updated: {server.name} by user {current_user.username}")
    return ServerResponse.model_validate(server)


@router.delete("/{server_id}")
async def delete_server(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    await db.delete(server)
    await db.commit()
    
    logger.info(f"Server deleted: {server.name} by user {current_user.username}")
    return {"message": f"Server {server.name} deleted successfully"}


@router.post("/{server_id}/test-connection")
async def test_server_connection(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Test SSH connection to server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    ssh_service = SSHService()
    connection_result = ssh_service.test_connection(server)
    
    return connection_result


@router.post("/{server_id}/restart-xray")
async def restart_xray_service(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Restart Xray service on server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    ssh_service = SSHService()
    restart_result = ssh_service.restart_xray_service(server)
    
    return restart_result


@router.get("/{server_id}/service-status")
async def get_service_status(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get Xray service status."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    ssh_service = SSHService()
    status_result = ssh_service.get_service_status(server)
    
    return status_result


@router.get("/{server_id}/config")
async def get_server_config(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get Xray configuration from server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    try:
        config_service = ConfigService()
        config = config_service.get_config(server)
        return config
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get configuration: {str(e)}"
        )


@router.get("/{server_id}/clients")
async def get_server_clients(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get clients from server configuration."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    try:
        config_service = ConfigService()
        clients = config_service.get_clients_from_config(server)
        return {"clients": clients}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get clients: {str(e)}"
        )


@router.post("/{server_id}/remove-expired")
async def remove_expired_clients(
    server_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Remove expired clients from server."""
    result = await db.execute(select(Server).where(Server.id == server_id))
    server = result.scalar_one_or_none()
    
    if not server:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Server not found"
        )
    
    try:
        config_service = ConfigService()
        removal_result = config_service.remove_expired_clients(server)
        
        # Restart Xray service if clients were removed
        if removal_result.get("success") and removal_result.get("removed_count", 0) > 0:
            ssh_service = SSHService()
            restart_result = ssh_service.restart_xray_service(server)
            removal_result["restart_result"] = restart_result
        
        return removal_result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove expired clients: {str(e)}"
        )
